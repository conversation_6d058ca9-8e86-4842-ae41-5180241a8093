#!/usr/bin/env python2
# -*- coding: utf-8 -*-

import time
import psutil
from prometheus_client import start_http_server, Gauge

# Define memory metrics
PROCESS_RSS_MEMORY = Gauge('process_rss_memory_bytes', 'Process Resident Set Size (RSS)', 
                          ['user', 'pid', 'name', 'cmdline'])
PROCESS_VMS_MEMORY = Gauge('process_vms_memory_bytes', 'Process Virtual Memory Size (VMS)', 
                          ['user', 'pid', 'name', 'cmdline'])
PROCESS_MEMORY_PERCENT = Gauge('process_memory_percent', 'Process Memory Usage Percentage',
                              ['user', 'pid', 'name', 'cmdline'])

# List of users to monitor
USERS_TO_MONITOR = ['master', 'worker']

def collect_memory_metrics():
    active_processes = set()
    
    # Dictionary for aggregating database processes
    db_processes = {}
    
    for user in USERS_TO_MONITOR:
        for proc in psutil.process_iter(['username', 'pid', 'memory_info', 'memory_percent', 'name']):
            try:
                if proc.info['name'] != 'sleep' and proc.info['username'] == user:
                    name = proc.info['name']
                    
                    # Check if it's a database process
                    if name in ['postgres', 'kingbase']:
                        # Use database name as aggregation key
                        db_key = (user, 'N/A', name, "aggregated_{0}_processes".format(name))
                        if db_key not in db_processes:
                            db_processes[db_key] = {
                                'rss': 0,
                                'vms': 0,
                                'memory_percent': 0
                            }
                            # Add to active_processes when first created
                            active_processes.add(db_key)
                        
                        # Accumulate memory metrics
                        mem_info = proc.info['memory_info']
                        db_processes[db_key]['rss'] += mem_info.rss
                        db_processes[db_key]['vms'] += mem_info.vms
                        db_processes[db_key]['memory_percent'] += proc.info['memory_percent']
                        continue
                    
                    # Original logic for non-database processes
                    pid = str(proc.info['pid'])
                    cmdline = ' '.join(proc.cmdline())[:200]
                    mem_info = proc.info['memory_info']
                    
                    process_key = (user, pid, name, cmdline)
                    active_processes.add(process_key)
                    
                    PROCESS_RSS_MEMORY.labels(user=user, pid=pid, name=name, cmdline=cmdline).set(mem_info.rss)
                    PROCESS_VMS_MEMORY.labels(user=user, pid=pid, name=name, cmdline=cmdline).set(mem_info.vms)
                    PROCESS_MEMORY_PERCENT.labels(user=user, pid=pid, name=name, cmdline=cmdline).set(proc.info['memory_percent'])
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

    # Process aggregated database metrics
    for db_key, stats in db_processes.items():
        user, pid, name, cmdline = db_key
        PROCESS_RSS_MEMORY.labels(user=user, pid=pid, name=name, cmdline=cmdline).set(stats['rss'])
        PROCESS_VMS_MEMORY.labels(user=user, pid=pid, name=name, cmdline=cmdline).set(stats['vms'])
        PROCESS_MEMORY_PERCENT.labels(user=user, pid=pid, name=name, cmdline=cmdline).set(stats['memory_percent'])

    # Clean up metrics for non-existent processes
    for item in PROCESS_RSS_MEMORY._metrics.copy():
        if item not in active_processes:
            try:
                # Attempt to remove the metrics from Prometheus Gauges
                PROCESS_RSS_MEMORY.remove(*item)
                PROCESS_VMS_MEMORY.remove(*item) 
                PROCESS_MEMORY_PERCENT.remove(*item)
                print("Successfully removed process metrics: User={0}, PID={1}, Name={2}".format(*item[:3]))
            except KeyError:
                pass
            except Exception as e:
                print("Error removing process metrics: User={}, PID={}, Name={}, cmdline={}".format(*item))
                print("Error removing process metrics: {0}".format(str(e)))

if __name__ == '__main__':
    # Start Prometheus HTTP server
    start_http_server(8005)
    
    print("Prometheus metrics server started on port 8005")
    
    while True:
        collect_memory_metrics()
        time.sleep(15)  # Collect data every 15 seconds