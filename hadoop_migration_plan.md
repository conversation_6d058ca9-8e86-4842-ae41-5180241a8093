# Hadoop生态迁移计划

## 📊 当前状态分析

### 内存使用情况
- **Hadoop核心服务**: ~14GB
  - NameNode (PID: 146062): ~400MB
  - DataNode (PID: 146676): ~400MB  
  - ResourceManager (PID: 146701): ~480MB
  - NodeManager (PID: 146671): ~435MB
  - SecondaryNameNode (PID: 146706): ~309MB
  - 其他Hadoop相关: ~12GB

- **Spark on YARN**: ~8GB
  - 多个ApplicationMaster和Executor进程

- **Flink集群**: ~4GB
  - JobManager和TaskManager进程

### 端口占用
- 8020: HDFS NameNode
- 8030: ResourceManager Scheduler
- 8040: NodeManager
- 8088: ResourceManager WebUI

## 🎯 迁移目标

### 架构变更
1. **移除Hadoop分布式文件系统** → 本地文件系统 + 对象存储
2. **移除YARN资源管理** → 直接进程管理
3. **Spark Standalone模式** → 替代Spark on YARN
4. **Flink单机模式** → 替代Flink集群模式

### 预期收益
- **内存节省**: ~26GB
- **架构简化**: 减少组件依赖
- **维护简化**: 减少故障点
- **性能提升**: 减少网络开销

## 📅 分阶段实施计划

### 阶段一：风险评估与准备 (2小时)

#### 1.1 数据依赖分析
```bash
# 检查HDFS数据使用情况
hdfs dfsadmin -report 2>/dev/null || echo "HDFS不可用"

# 检查Spark应用依赖
ps aux | grep spark | grep -o "hdfs://[^[:space:]]*" | sort -u

# 检查Flink应用依赖  
ps aux | grep flink | grep -o "hdfs://[^[:space:]]*" | sort -u
```

#### 1.2 配置文件备份
```bash
# 创建备份目录
mkdir -p /backup/hadoop_migration_$(date +%Y%m%d_%H%M%S)

# 备份关键配置
find /home -name "*.conf" -o -name "*.properties" -o -name "*.yaml" | \
  grep -E "(spark|flink|hadoop)" | \
  xargs -I {} cp {} /backup/hadoop_migration_*/
```

#### 1.3 服务依赖映射
```bash
# 检查systemd服务依赖
systemctl list-dependencies | grep -E "(hadoop|spark|flink)"

# 检查进程依赖关系
pstree -p | grep -E "(hadoop|spark|flink)"
```

### 阶段二：测试性停止 (30分钟)

#### 2.1 优雅停止Hadoop服务
```bash
# 停止顺序：应用 → YARN → HDFS
# 1. 停止Spark应用
yarn application -list | grep RUNNING | awk '{print $1}' | xargs -I {} yarn application -kill {}

# 2. 停止Flink应用  
flink list | grep RUNNING | awk '{print $4}' | xargs -I {} flink cancel {}

# 3. 停止YARN
stop-yarn.sh

# 4. 停止HDFS
stop-dfs.sh
```

#### 2.2 监控系统状态
```bash
# 监控内存释放
watch -n 5 'free -h'

# 检查端口释放
netstat -tlnp | grep -E "(8020|8030|8040|8088)"

# 检查进程清理
ps aux | grep -E "(hadoop|yarn)" | grep -v grep
```

#### 2.3 验证核心服务
```bash
# 验证ClickHouse正常
clickhouse-client --query "SELECT 1"

# 验证Elasticsearch正常  
curl -X GET "localhost:9200/_cluster/health"

# 验证Kafka正常
kafka-topics.sh --list --bootstrap-server localhost:9092
```

### 阶段三：架构重构 (4小时)

#### 3.1 Spark Standalone部署
```bash
# 创建Spark Standalone配置
cat > /opt/spark/conf/spark-defaults.conf << EOF
spark.master                     spark://localhost:7077
spark.sql.adaptive.enabled       true
spark.sql.adaptive.coalescePartitions.enabled true
spark.serializer                 org.apache.spark.serializer.KryoSerializer
spark.driver.memory              2g
spark.executor.memory            2g
spark.executor.cores             2
spark.executor.instances         2
EOF

# 启动Spark Master和Worker
start-master.sh
start-worker.sh spark://localhost:7077
```

#### 3.2 Flink单机部署
```bash
# 配置Flink单机模式
cat > /opt/flink/conf/flink-conf.yaml << EOF
jobmanager.rpc.address: localhost
jobmanager.rpc.port: 6123
jobmanager.memory.process.size: 1024m
taskmanager.memory.process.size: 2048m
taskmanager.numberOfTaskSlots: 4
parallelism.default: 2
EOF

# 启动Flink
start-cluster.sh
```

#### 3.3 数据存储重构
```bash
# 创建本地数据目录
mkdir -p /data/{clickhouse,elasticsearch,kafka,logs}

# 配置ClickHouse本地存储
# 配置Elasticsearch本地索引
# 配置Kafka本地日志
```

### 阶段四：应用迁移 (2小时)

#### 4.1 更新应用配置
- 修改Spark应用的master URL
- 更新Flink应用的集群配置
- 调整数据路径从HDFS到本地文件系统

#### 4.2 重启应用服务
- 按依赖顺序重启各个应用
- 验证数据流正常
- 检查日志无错误

### 阶段五：清理与优化 (1小时)

#### 5.1 清理Hadoop残留
```bash
# 清理Hadoop进程
pkill -f "org.apache.hadoop"

# 清理Hadoop数据目录
rm -rf /tmp/hadoop-*

# 清理Hadoop配置
# (保留备份，删除运行时配置)
```

#### 5.2 系统优化
```bash
# 调整系统参数
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=150' >> /etc/sysctl.conf
sysctl -p

# 优化文件描述符限制
echo '* soft nofile 65536' >> /etc/security/limits.conf
echo '* hard nofile 65536' >> /etc/security/limits.conf
```

## ⚠️ 风险控制

### 回滚计划
1. **配置备份**: 所有配置文件已备份
2. **数据备份**: 关键数据已备份到安全位置
3. **快速回滚**: 准备一键恢复脚本

### 监控检查点
1. **内存使用**: 确保内存使用下降到预期水平
2. **应用状态**: 所有关键应用正常运行
3. **数据完整性**: 验证数据无丢失
4. **性能指标**: 确保性能不降反升

## 📈 预期结果

### 内存优化
- **当前**: 96GB总内存，52GB应用使用
- **优化后**: 96GB总内存，26GB应用使用  
- **节省**: 26GB内存 (27%优化)

### 架构简化
- **组件减少**: 从15+个Hadoop组件减少到0个
- **依赖简化**: 消除复杂的分布式依赖
- **维护简化**: 减少故障排查复杂度

### 性能提升
- **网络开销**: 消除HDFS网络IO
- **资源调度**: 消除YARN调度开销
- **启动速度**: 应用启动更快
