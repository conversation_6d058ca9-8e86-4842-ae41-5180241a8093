#!/bin/bash

# Spark和Flink Standalone运行时配置脚本
# 基于容器内现有安装，配置优化的Standalone环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%H:%M:%S') $1"
}

# 配置变量 - 基于容器内实际安装
CONTAINER_NAME="bsa_platform"
SPARK_HOME="/home/<USER>/spark"
FLINK_HOME="/home/<USER>/flink"
JAVA_HOME="/home/<USER>/java/jdk"
APP_BASE="/home/<USER>/ISOP/apps/dataconfig"

# 下载和安装Spark
install_spark() {
    log_info "安装Spark Standalone..."
    
    if [ -d "$SPARK_HOME" ]; then
        log_warn "Spark目录已存在，跳过安装"
        return 0
    fi
    
    cd /tmp
    
    # 下载Spark (如果网络不可用，可以手动上传)
    SPARK_TGZ="spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz"
    if [ ! -f "$SPARK_TGZ" ]; then
        log_info "下载Spark $SPARK_VERSION..."
        wget "https://archive.apache.org/dist/spark/spark-${SPARK_VERSION}/${SPARK_TGZ}" || {
            log_warn "下载失败，请手动下载并放置到 /tmp/$SPARK_TGZ"
            return 1
        }
    fi
    
    # 解压安装
    tar -xzf "$SPARK_TGZ"
    mv "spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}" "$SPARK_HOME"
    
    # 设置权限
    chown -R root:root "$SPARK_HOME"
    chmod +x "$SPARK_HOME"/bin/*
    chmod +x "$SPARK_HOME"/sbin/*
    
    log_success "Spark安装完成: $SPARK_HOME"
}

# 下载和安装Flink
install_flink() {
    log_info "安装Flink Standalone..."
    
    if [ -d "$FLINK_HOME" ]; then
        log_warn "Flink目录已存在，跳过安装"
        return 0
    fi
    
    cd /tmp
    
    # 下载Flink
    FLINK_TGZ="flink-${FLINK_VERSION}-bin-scala_${SCALA_VERSION}.tgz"
    if [ ! -f "$FLINK_TGZ" ]; then
        log_info "下载Flink $FLINK_VERSION..."
        wget "https://archive.apache.org/dist/flink/flink-${FLINK_VERSION}/${FLINK_TGZ}" || {
            log_warn "下载失败，请手动下载并放置到 /tmp/$FLINK_TGZ"
            return 1
        }
    fi
    
    # 解压安装
    tar -xzf "$FLINK_TGZ"
    mv "flink-${FLINK_VERSION}" "$FLINK_HOME"
    
    # 设置权限
    chown -R root:root "$FLINK_HOME"
    chmod +x "$FLINK_HOME"/bin/*
    
    log_success "Flink安装完成: $FLINK_HOME"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    # 创建环境配置文件
    cat > /etc/profile.d/spark-flink.sh << EOF
# Spark和Flink环境变量
export JAVA_HOME="$JAVA_HOME"
export SPARK_HOME="$SPARK_HOME"
export FLINK_HOME="$FLINK_HOME"
export PATH=\$PATH:\$SPARK_HOME/bin:\$SPARK_HOME/sbin:\$FLINK_HOME/bin
EOF
    
    # 使环境变量生效
    source /etc/profile.d/spark-flink.sh
    
    log_success "环境变量配置完成"
}

# 创建优化的Spark配置
create_optimized_spark_config() {
    log_info "创建优化的Spark配置..."
    
    # spark-defaults.conf - 内存优化版本
    cat > "$SPARK_HOME/conf/spark-defaults.conf" << EOF
# Spark Standalone优化配置 - 内存节省版本
spark.master                     spark://localhost:7077

# 内存优化 - 相比YARN模式节省50%内存
spark.driver.memory              256m
spark.driver.maxResultSize       128m
spark.executor.memory            512m
spark.executor.cores             1
spark.executor.instances         2

# 性能优化
spark.sql.adaptive.enabled       true
spark.sql.adaptive.coalescePartitions.enabled true
spark.serializer                 org.apache.spark.serializer.KryoSerializer

# 网络超时
spark.network.timeout            300s
spark.rpc.askTimeout             300s

# 动态资源分配
spark.dynamicAllocation.enabled          true
spark.dynamicAllocation.minExecutors     1
spark.dynamicAllocation.maxExecutors     2
spark.dynamicAllocation.initialExecutors 1

# Kafka优化
spark.streaming.kafka.maxRatePerPartition 500
spark.streaming.backpressure.enabled      true
EOF

    # spark-env.sh - 内存优化版本
    cat > "$SPARK_HOME/conf/spark-env.sh" << EOF
#!/usr/bin/env bash

export JAVA_HOME="$JAVA_HOME"
export SPARK_HOME="$SPARK_HOME"

# Master配置 - 减少内存使用
export SPARK_MASTER_HOST=localhost
export SPARK_MASTER_PORT=7077
export SPARK_MASTER_WEBUI_PORT=8080

# Worker配置 - 优化资源分配
export SPARK_WORKER_CORES=2
export SPARK_WORKER_MEMORY=2g
export SPARK_WORKER_PORT=7078
export SPARK_WORKER_WEBUI_PORT=8081
export SPARK_WORKER_DIR=$SPARK_HOME/work

# 日志和PID目录
export SPARK_LOG_DIR=$SPARK_HOME/logs
export SPARK_PID_DIR=$SPARK_HOME/pid

# JVM优化 - 减少内存占用
export SPARK_MASTER_OPTS="-XX:+UseG1GC -XX:+UseCompressedOops"
export SPARK_WORKER_OPTS="-XX:+UseG1GC -XX:+UseCompressedOops"
export SPARK_DRIVER_OPTS="-XX:+UseG1GC -XX:+UseCompressedOops"
export SPARK_EXECUTOR_OPTS="-XX:+UseG1GC -XX:+UseCompressedOops"
EOF

    chmod +x "$SPARK_HOME/conf/spark-env.sh"
    
    # 创建必要目录
    mkdir -p "$SPARK_HOME"/{logs,work,pid}
    
    log_success "Spark优化配置完成"
}

# 创建优化的Flink配置
create_optimized_flink_config() {
    log_info "创建优化的Flink配置..."
    
    # flink-conf.yaml - 内存优化版本
    cat > "$FLINK_HOME/conf/flink-conf.yaml" << EOF
# Flink Standalone优化配置 - 内存节省版本

# JobManager配置 - 减少内存使用
jobmanager.rpc.address: localhost
jobmanager.rpc.port: 6123
jobmanager.bind-host: 0.0.0.0
jobmanager.memory.process.size: 512m
jobmanager.memory.heap.size: 256m

# TaskManager配置 - 优化内存分配
taskmanager.bind-host: 0.0.0.0
taskmanager.host: localhost
taskmanager.rpc.port: 6122
taskmanager.memory.process.size: 1024m
taskmanager.memory.task.heap.size: 512m
taskmanager.memory.managed.size: 256m
taskmanager.numberOfTaskSlots: 2

# 网络配置 - 减少网络缓冲区
taskmanager.network.memory.min: 32mb
taskmanager.network.memory.max: 64mb
taskmanager.network.memory.fraction: 0.05

# 检查点配置
state.backend: filesystem
state.checkpoints.dir: file://$FLINK_HOME/checkpoints
state.savepoints.dir: file://$FLINK_HOME/savepoints

# 并行度配置
parallelism.default: 1

# Web UI配置
rest.port: 8082
rest.bind-address: 0.0.0.0

# 高可用配置(单机模式关闭)
high-availability: NONE

# 日志配置
env.log.dir: $FLINK_HOME/log
EOF

    # 创建必要目录
    mkdir -p "$FLINK_HOME"/{log,checkpoints,savepoints}
    
    log_success "Flink优化配置完成"
}

# 创建快速启动脚本
create_quick_start_scripts() {
    log_info "创建快速启动脚本..."
    
    # 创建应用目录
    mkdir -p /opt/apps/{scripts,logs,pid}
    
    # 快速启动所有服务
    cat > /opt/apps/scripts/start_all.sh << 'EOF'
#!/bin/bash

echo "启动Spark和Flink Standalone集群..."

# 启动Spark
echo "启动Spark Master..."
/opt/spark/sbin/start-master.sh

sleep 3

echo "启动Spark Worker..."
/opt/spark/sbin/start-worker.sh spark://localhost:7077

sleep 3

# 启动Flink
echo "启动Flink集群..."
/opt/flink/bin/start-cluster.sh

sleep 5

echo ""
echo "=========================================="
echo "集群启动完成!"
echo "=========================================="
echo "Spark Master UI: http://localhost:8080"
echo "Flink Dashboard: http://localhost:8082"
echo ""
echo "检查状态:"
echo "  Spark: jps | grep -E '(Master|Worker)'"
echo "  Flink: jps | grep -E '(StandaloneSession|TaskManager)'"
echo "=========================================="
EOF

    # 快速停止所有服务
    cat > /opt/apps/scripts/stop_all.sh << 'EOF'
#!/bin/bash

echo "停止Spark和Flink集群..."

# 停止Spark
echo "停止Spark集群..."
/opt/spark/sbin/stop-all.sh

# 停止Flink
echo "停止Flink集群..."
/opt/flink/bin/stop-cluster.sh

echo "所有服务已停止"
EOF

    # 状态检查脚本
    cat > /opt/apps/scripts/status.sh << 'EOF'
#!/bin/bash

echo "=========================================="
echo "Spark & Flink 集群状态"
echo "=========================================="

echo ""
echo "=== Java进程 ==="
jps | grep -E "(Master|Worker|StandaloneSession|TaskManager)" || echo "未发现Spark/Flink进程"

echo ""
echo "=== 端口监听 ==="
echo "Spark Master (7077): $(netstat -tln | grep :7077 >/dev/null && echo '✓ 监听中' || echo '✗ 未监听')"
echo "Spark Web UI (8080): $(netstat -tln | grep :8080 >/dev/null && echo '✓ 监听中' || echo '✗ 未监听')"
echo "Flink JobManager (6123): $(netstat -tln | grep :6123 >/dev/null && echo '✓ 监听中' || echo '✗ 未监听')"
echo "Flink Web UI (8082): $(netstat -tln | grep :8082 >/dev/null && echo '✓ 监听中' || echo '✗ 未监听')"

echo ""
echo "=== 内存使用 ==="
echo "Spark进程内存: $(ps aux | grep -E 'spark.*Master|spark.*Worker' | grep -v grep | awk '{sum+=$6} END {print (sum/1024) " MB"}' || echo '0 MB')"
echo "Flink进程内存: $(ps aux | grep -E 'flink.*StandaloneSession|flink.*TaskManager' | grep -v grep | awk '{sum+=$6} END {print (sum/1024) " MB"}' || echo '0 MB')"

echo ""
echo "=========================================="
EOF

    chmod +x /opt/apps/scripts/*.sh
    
    log_success "快速启动脚本创建完成"
}

# 创建应用迁移脚本
create_app_migration_script() {
    log_info "创建应用迁移脚本..."
    
    cat > /opt/apps/scripts/migrate_apps.sh << 'EOF'
#!/bin/bash

# 应用迁移脚本 - 将YARN应用迁移到Standalone模式

APP_BASE="/home/<USER>/ISOP/apps/dataconfig"
SPARK_HOME="/opt/spark"

echo "=========================================="
echo "应用迁移到Standalone模式"
echo "=========================================="

# 检查JAR文件是否存在
check_jar_files() {
    echo "检查应用JAR文件..."
    
    if [ ! -f "$APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar" ]; then
        echo "错误: 持久化应用JAR不存在"
        return 1
    fi
    
    if [ ! -f "$APP_BASE/lib/parser/bsa-setl-0.1.jar" ]; then
        echo "错误: 解析器应用JAR不存在"
        return 1
    fi
    
    echo "JAR文件检查通过"
}

# 启动ES持久化应用
start_es_persistent() {
    echo "启动ES持久化应用..."
    
    nohup $SPARK_HOME/bin/spark-submit \
        --class com.nsfocus.bsad.etl.plugin.core.PersistentEngine \
        --master spark://localhost:7077 \
        --driver-memory 256m \
        --executor-memory 512m \
        --executor-cores 1 \
        --total-executor-cores 1 \
        --name BSA_ES_PERSISTENT_TOTAL \
        $APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar \
        -duration 5000 -sink-plugs es -name BSA_ES_PERSISTENT_TOTAL \
        > /opt/apps/logs/es_persistent.log 2>&1 &
    
    echo $! > /opt/apps/pid/es_persistent.pid
    echo "ES持久化应用已启动 (PID: $!)"
}

# 启动CK流量持久化应用
start_ck_traffic() {
    echo "启动CK流量持久化应用..."
    
    nohup $SPARK_HOME/bin/spark-submit \
        --class com.nsfocus.bsad.etl.plugin.core.PersistentEngine \
        --master spark://localhost:7077 \
        --driver-memory 256m \
        --executor-memory 512m \
        --executor-cores 1 \
        --total-executor-cores 1 \
        --name BSA_CK_PERSISTENT_TRAFFIC \
        $APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar \
        -duration 5000 -sink-plugs ck -queue traffic -name BSA_CK_PERSISTENT_TRAFFIC \
        > /opt/apps/logs/ck_traffic.log 2>&1 &
    
    echo $! > /opt/apps/pid/ck_traffic.pid
    echo "CK流量持久化应用已启动 (PID: $!)"
}

# 启动CK告警持久化应用
start_ck_alarm() {
    echo "启动CK告警持久化应用..."
    
    nohup $SPARK_HOME/bin/spark-submit \
        --class com.nsfocus.bsad.etl.plugin.core.PersistentEngine \
        --master spark://localhost:7077 \
        --driver-memory 256m \
        --executor-memory 512m \
        --executor-cores 1 \
        --total-executor-cores 1 \
        --name BSA_CK_PERSISTENT_ALARM \
        $APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar \
        -duration 5000 -sink-plugs ck -queue alarm -name BSA_CK_PERSISTENT_ALARM \
        > /opt/apps/logs/ck_alarm.log 2>&1 &
    
    echo $! > /opt/apps/pid/ck_alarm.pid
    echo "CK告警持久化应用已启动 (PID: $!)"
}

# 启动解析器应用
start_parser() {
    echo "启动解析器应用..."
    
    nohup $SPARK_HOME/bin/spark-submit \
        --class com.nsfocus.bsa.setl.App \
        --master spark://localhost:7077 \
        --driver-memory 256m \
        --executor-memory 512m \
        --executor-cores 1 \
        --total-executor-cores 1 \
        --name ISOP_PARSER \
        $APP_BASE/lib/parser/bsa-setl-0.1.jar \
        ISOP_PARSER -duration 2000 $(date +%s)000 \
        > /opt/apps/logs/parser.log 2>&1 &
    
    echo $! > /opt/apps/pid/parser.pid
    echo "解析器应用已启动 (PID: $!)"
}

# 主函数
main() {
    # 检查Spark集群是否运行
    if ! jps | grep Master >/dev/null; then
        echo "错误: Spark Master未运行，请先启动集群"
        echo "运行: /opt/apps/scripts/start_all.sh"
        exit 1
    fi
    
    check_jar_files || exit 1
    
    mkdir -p /opt/apps/{logs,pid}
    
    start_es_persistent
    sleep 3
    
    start_ck_traffic
    sleep 3
    
    start_ck_alarm
    sleep 3
    
    start_parser
    
    echo ""
    echo "=========================================="
    echo "应用迁移完成!"
    echo "=========================================="
    echo "查看应用状态: /opt/apps/scripts/status.sh"
    echo "查看日志: tail -f /opt/apps/logs/*.log"
    echo "Spark UI: http://localhost:8080"
    echo "=========================================="
}

main "$@"
EOF

    chmod +x /opt/apps/scripts/migrate_apps.sh
    
    log_success "应用迁移脚本创建完成"
}

# 主函数
main() {
    echo "=========================================="
    echo "Spark & Flink Standalone 安装部署"
    echo "=========================================="
    
    # 检查权限
    if [[ $EUID -ne 0 ]]; then
        echo "错误: 请以root权限运行此脚本"
        exit 1
    fi
    
    # 检查Java环境
    if [ ! -d "$JAVA_HOME" ]; then
        echo "错误: Java环境不存在: $JAVA_HOME"
        exit 1
    fi
    
    install_spark
    install_flink
    setup_environment
    create_optimized_spark_config
    create_optimized_flink_config
    create_quick_start_scripts
    create_app_migration_script
    
    echo ""
    echo "=========================================="
    echo "安装完成!"
    echo "=========================================="
    echo ""
    echo "下一步操作:"
    echo "1. 启动集群: /opt/apps/scripts/start_all.sh"
    echo "2. 检查状态: /opt/apps/scripts/status.sh"
    echo "3. 迁移应用: /opt/apps/scripts/migrate_apps.sh"
    echo "4. 停止集群: /opt/apps/scripts/stop_all.sh"
    echo ""
    echo "预期内存使用:"
    echo "- Spark集群: ~768MB (Master: 256MB + Worker: 512MB)"
    echo "- Flink集群: ~768MB (JobManager: 256MB + TaskManager: 512MB)"
    echo "- 应用总计: ~2GB (4个Spark应用，每个512MB)"
    echo "- 总计: ~3.5GB (相比YARN模式节省8GB+)"
    echo ""
    echo "Web界面:"
    echo "- Spark: http://localhost:8080"
    echo "- Flink: http://localhost:8082"
    echo "=========================================="
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
