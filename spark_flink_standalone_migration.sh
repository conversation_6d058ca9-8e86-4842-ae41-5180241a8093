#!/bin/bash

# Spark和Flink Standalone模式迁移脚本
# 将现有的YARN模式应用迁移到Standalone模式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%H:%M:%S') $1"
}

# 全局配置
SPARK_HOME="/opt/spark"
FLINK_HOME="/opt/flink"
BACKUP_DIR="/backup/standalone_migration_$(date +%Y%m%d_%H%M%S)"
JAVA_HOME="/home/<USER>/java/jdk"

# 创建目录结构
create_directories() {
    log_info "创建Standalone模式目录结构..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$SPARK_HOME"/{conf,logs,work,pid}
    mkdir -p "$FLINK_HOME"/{conf,log,lib}
    mkdir -p /opt/apps/{persistent,parser,engine}
    
    log_success "目录结构创建完成"
}

# 分析当前Spark应用
analyze_current_spark_apps() {
    log_info "分析当前Spark应用..."
    
    cat > "$BACKUP_DIR/current_spark_apps.txt" << EOF
当前Spark应用分析
==================

1. BSA_ES_PERSISTENT_TOTAL (application_1750300790824_0003)
   - 主类: com.nsfocus.bsad.etl.plugin.core.PersistentEngine
   - JAR: /home/<USER>/ISOP/apps/dataconfig/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar
   - 参数: -duration 5000 -sink-plugs es -name BSA_ES_PERSISTENT_TOTAL
   - 内存: ApplicationMaster(1GB) + Executor(2GB)

2. BSA_CK_PERSISTENT_TRAFFIC (application_1750300790824_0004)
   - 主类: com.nsfocus.bsad.etl.plugin.core.PersistentEngine
   - JAR: /home/<USER>/ISOP/apps/dataconfig/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar
   - 参数: -duration 5000 -sink-plugs ck -queue traffic -name BSA_CK_PERSISTENT_TRAFFIC
   - 内存: ApplicationMaster(1GB) + Executor(2GB)

3. BSA_CK_PERSISTENT_ALARM (application_1750300790824_0005)
   - 主类: com.nsfocus.bsad.etl.plugin.core.PersistentEngine
   - JAR: /home/<USER>/ISOP/apps/dataconfig/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar
   - 参数: -duration 5000 -sink-plugs ck -queue alarm -name BSA_CK_PERSISTENT_ALARM
   - 内存: ApplicationMaster(1GB) + Executor(2GB)

4. ISOP_PARSER (application_1750300790824_0008)
   - 主类: com.nsfocus.bsa.setl.App
   - JAR: /home/<USER>/ISOP/apps/dataconfig/lib/parser/bsa-setl-0.1.jar
   - 参数: ISOP_PARSER -duration 2000 1750313735000
   - 内存: ApplicationMaster(1GB) + Executor(1GB)

总内存使用: ~12GB
EOF

    log_success "Spark应用分析完成: $BACKUP_DIR/current_spark_apps.txt"
}

# 分析当前Flink应用
analyze_current_flink_apps() {
    log_info "分析当前Flink应用..."
    
    cat > "$BACKUP_DIR/current_flink_apps.txt" << EOF
当前Flink应用分析
==================

1. 数据处理引擎 (StandaloneApplicationClusterEntryPoint)
   - 主类: com.ngengine.flink.main.Main
   - 内存配置:
     * JobManager: 1024MB (heap: 469MB, off-heap: 134MB)
     * TaskManager: 2048MB (heap: 1229MB, network: 166MB)
   - 任务槽: 2个
   - 端口: JobManager RPC(33579), REST(44637)

总内存使用: ~3GB
EOF

    log_success "Flink应用分析完成: $BACKUP_DIR/current_flink_apps.txt"
}

# 创建Spark Standalone配置
create_spark_standalone_config() {
    log_info "创建Spark Standalone配置..."
    
    # spark-defaults.conf
    cat > "$SPARK_HOME/conf/spark-defaults.conf" << EOF
# Spark Standalone模式配置
# 替代YARN模式，优化内存使用

# 集群配置
spark.master                     spark://localhost:7077
spark.deploy.recoveryMode        FILESYSTEM
spark.deploy.recoveryDirectory   $SPARK_HOME/recovery

# 内存优化配置
spark.driver.memory              512m
spark.driver.maxResultSize       256m
spark.executor.memory            1g
spark.executor.cores             1
spark.executor.instances         2

# 性能优化
spark.sql.adaptive.enabled       true
spark.sql.adaptive.coalescePartitions.enabled true
spark.serializer                 org.apache.spark.serializer.KryoSerializer
spark.kryo.registrationRequired  false

# 网络配置
spark.network.timeout            300s
spark.rpc.askTimeout             300s
spark.rpc.lookupTimeout          300s

# 日志配置
spark.eventLog.enabled           true
spark.eventLog.dir               $SPARK_HOME/logs/events
spark.history.fs.logDirectory    $SPARK_HOME/logs/events

# 动态资源分配
spark.dynamicAllocation.enabled          true
spark.dynamicAllocation.minExecutors     1
spark.dynamicAllocation.maxExecutors     3
spark.dynamicAllocation.initialExecutors 2

# Kafka连接配置
spark.streaming.kafka.maxRatePerPartition 1000
spark.streaming.backpressure.enabled      true
EOF

    # spark-env.sh
    cat > "$SPARK_HOME/conf/spark-env.sh" << EOF
#!/usr/bin/env bash

# Java配置
export JAVA_HOME="$JAVA_HOME"
export SPARK_HOME="$SPARK_HOME"

# Master配置
export SPARK_MASTER_HOST=localhost
export SPARK_MASTER_PORT=7077
export SPARK_MASTER_WEBUI_PORT=8080

# Worker配置
export SPARK_WORKER_CORES=4
export SPARK_WORKER_MEMORY=4g
export SPARK_WORKER_PORT=7078
export SPARK_WORKER_WEBUI_PORT=8081
export SPARK_WORKER_DIR=$SPARK_HOME/work

# 日志配置
export SPARK_LOG_DIR=$SPARK_HOME/logs
export SPARK_PID_DIR=$SPARK_HOME/pid

# JVM配置
export SPARK_MASTER_OPTS="-Xmx512m -XX:+UseG1GC"
export SPARK_WORKER_OPTS="-Xmx1g -XX:+UseG1GC"
export SPARK_DRIVER_OPTS="-XX:+UseG1GC"
export SPARK_EXECUTOR_OPTS="-XX:+UseG1GC"
EOF

    chmod +x "$SPARK_HOME/conf/spark-env.sh"
    
    log_success "Spark Standalone配置创建完成"
}

# 创建Flink Standalone配置
create_flink_standalone_config() {
    log_info "创建Flink Standalone配置..."
    
    # flink-conf.yaml
    cat > "$FLINK_HOME/conf/flink-conf.yaml" << EOF
# Flink Standalone模式配置
# 优化单机部署，减少内存使用

# JobManager配置
jobmanager.rpc.address: localhost
jobmanager.rpc.port: 6123
jobmanager.bind-host: 0.0.0.0
jobmanager.memory.process.size: 768m
jobmanager.memory.heap.size: 512m

# TaskManager配置
taskmanager.bind-host: 0.0.0.0
taskmanager.host: localhost
taskmanager.rpc.port: 6122
taskmanager.memory.process.size: 1536m
taskmanager.memory.task.heap.size: 1024m
taskmanager.memory.managed.size: 256m
taskmanager.numberOfTaskSlots: 2

# 网络配置
taskmanager.network.memory.min: 64mb
taskmanager.network.memory.max: 128mb
taskmanager.network.memory.fraction: 0.1

# 检查点配置
state.backend: filesystem
state.checkpoints.dir: file://$FLINK_HOME/checkpoints
state.savepoints.dir: file://$FLINK_HOME/savepoints

# 并行度配置
parallelism.default: 2

# Web UI配置
rest.port: 8082
rest.bind-address: 0.0.0.0

# 高可用配置(单机模式关闭)
high-availability: NONE

# 日志配置
env.log.dir: $FLINK_HOME/log
EOF

    log_success "Flink Standalone配置创建完成"
}

# 创建应用启动脚本
create_application_scripts() {
    log_info "创建应用启动脚本..."
    
    # Spark应用启动脚本
    cat > "/opt/apps/start_spark_apps.sh" << 'EOF'
#!/bin/bash

# Spark应用启动脚本 - Standalone模式

SPARK_HOME="/opt/spark"
JAVA_HOME="/home/<USER>/java/jdk"
APP_BASE="/home/<USER>/ISOP/apps/dataconfig"

# 启动Spark Master和Worker
start_spark_cluster() {
    echo "启动Spark集群..."
    $SPARK_HOME/sbin/start-master.sh
    sleep 5
    $SPARK_HOME/sbin/start-worker.sh spark://localhost:7077
    sleep 5
    echo "Spark集群启动完成"
}

# 启动ES持久化应用
start_es_persistent() {
    echo "启动ES持久化应用..."
    nohup $SPARK_HOME/bin/spark-submit \
        --class com.nsfocus.bsad.etl.plugin.core.PersistentEngine \
        --master spark://localhost:7077 \
        --driver-memory 512m \
        --executor-memory 1g \
        --executor-cores 1 \
        --total-executor-cores 2 \
        --name BSA_ES_PERSISTENT_TOTAL \
        $APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar \
        -duration 5000 -sink-plugs es -name BSA_ES_PERSISTENT_TOTAL \
        > /opt/apps/logs/es_persistent.log 2>&1 &
    
    echo $! > /opt/apps/pid/es_persistent.pid
    echo "ES持久化应用已启动"
}

# 启动CK流量持久化应用
start_ck_traffic_persistent() {
    echo "启动CK流量持久化应用..."
    nohup $SPARK_HOME/bin/spark-submit \
        --class com.nsfocus.bsad.etl.plugin.core.PersistentEngine \
        --master spark://localhost:7077 \
        --driver-memory 512m \
        --executor-memory 1g \
        --executor-cores 1 \
        --total-executor-cores 2 \
        --name BSA_CK_PERSISTENT_TRAFFIC \
        $APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar \
        -duration 5000 -sink-plugs ck -queue traffic -name BSA_CK_PERSISTENT_TRAFFIC \
        > /opt/apps/logs/ck_traffic_persistent.log 2>&1 &
    
    echo $! > /opt/apps/pid/ck_traffic_persistent.pid
    echo "CK流量持久化应用已启动"
}

# 启动CK告警持久化应用
start_ck_alarm_persistent() {
    echo "启动CK告警持久化应用..."
    nohup $SPARK_HOME/bin/spark-submit \
        --class com.nsfocus.bsad.etl.plugin.core.PersistentEngine \
        --master spark://localhost:7077 \
        --driver-memory 512m \
        --executor-memory 1g \
        --executor-cores 1 \
        --total-executor-cores 2 \
        --name BSA_CK_PERSISTENT_ALARM \
        $APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar \
        -duration 5000 -sink-plugs ck -queue alarm -name BSA_CK_PERSISTENT_ALARM \
        > /opt/apps/logs/ck_alarm_persistent.log 2>&1 &
    
    echo $! > /opt/apps/pid/ck_alarm_persistent.pid
    echo "CK告警持久化应用已启动"
}

# 启动解析器应用
start_parser() {
    echo "启动解析器应用..."
    nohup $SPARK_HOME/bin/spark-submit \
        --class com.nsfocus.bsa.setl.App \
        --master spark://localhost:7077 \
        --driver-memory 512m \
        --executor-memory 1g \
        --executor-cores 1 \
        --total-executor-cores 2 \
        --name ISOP_PARSER \
        $APP_BASE/lib/parser/bsa-setl-0.1.jar \
        ISOP_PARSER -duration 2000 $(date +%s)000 \
        > /opt/apps/logs/parser.log 2>&1 &
    
    echo $! > /opt/apps/pid/parser.pid
    echo "解析器应用已启动"
}

# 主函数
main() {
    mkdir -p /opt/apps/{logs,pid}
    
    echo "=========================================="
    echo "启动Spark Standalone应用"
    echo "=========================================="
    
    start_spark_cluster
    sleep 10
    
    start_es_persistent
    sleep 5
    
    start_ck_traffic_persistent
    sleep 5
    
    start_ck_alarm_persistent
    sleep 5
    
    start_parser
    
    echo ""
    echo "所有应用启动完成!"
    echo "查看状态: $SPARK_HOME/sbin/start-master.sh status"
    echo "Web UI: http://localhost:8080"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
EOF

    # Flink应用启动脚本
    cat > "/opt/apps/start_flink_apps.sh" << 'EOF'
#!/bin/bash

# Flink应用启动脚本 - Standalone模式

FLINK_HOME="/opt/flink"
JAVA_HOME="/home/<USER>/java/jdk"

# 启动Flink集群
start_flink_cluster() {
    echo "启动Flink集群..."
    $FLINK_HOME/bin/start-cluster.sh
    sleep 10
    echo "Flink集群启动完成"
}

# 启动数据处理引擎
start_data_engine() {
    echo "启动数据处理引擎..."
    
    # 这里需要根据实际的Flink应用JAR和主类进行调整
    # 当前运行的是 com.ngengine.flink.main.Main
    
    nohup $FLINK_HOME/bin/flink run \
        --class com.ngengine.flink.main.Main \
        --parallelism 2 \
        /path/to/your/flink/app.jar \
        > /opt/apps/logs/flink_engine.log 2>&1 &
    
    echo $! > /opt/apps/pid/flink_engine.pid
    echo "数据处理引擎已启动"
}

# 主函数
main() {
    mkdir -p /opt/apps/{logs,pid}
    
    echo "=========================================="
    echo "启动Flink Standalone应用"
    echo "=========================================="
    
    start_flink_cluster
    sleep 10
    
    # start_data_engine  # 需要找到实际的JAR文件路径
    
    echo ""
    echo "Flink集群启动完成!"
    echo "Web UI: http://localhost:8082"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
EOF

    chmod +x /opt/apps/start_spark_apps.sh
    chmod +x /opt/apps/start_flink_apps.sh
    
    log_success "应用启动脚本创建完成"
}

# 创建停止脚本
create_stop_scripts() {
    log_info "创建停止脚本..."
    
    cat > "/opt/apps/stop_all_apps.sh" << 'EOF'
#!/bin/bash

# 停止所有Standalone应用

SPARK_HOME="/opt/spark"
FLINK_HOME="/opt/flink"

echo "停止Spark应用..."
if [ -f /opt/apps/pid/es_persistent.pid ]; then
    kill $(cat /opt/apps/pid/es_persistent.pid) 2>/dev/null || true
    rm -f /opt/apps/pid/es_persistent.pid
fi

if [ -f /opt/apps/pid/ck_traffic_persistent.pid ]; then
    kill $(cat /opt/apps/pid/ck_traffic_persistent.pid) 2>/dev/null || true
    rm -f /opt/apps/pid/ck_traffic_persistent.pid
fi

if [ -f /opt/apps/pid/ck_alarm_persistent.pid ]; then
    kill $(cat /opt/apps/pid/ck_alarm_persistent.pid) 2>/dev/null || true
    rm -f /opt/apps/pid/ck_alarm_persistent.pid
fi

if [ -f /opt/apps/pid/parser.pid ]; then
    kill $(cat /opt/apps/pid/parser.pid) 2>/dev/null || true
    rm -f /opt/apps/pid/parser.pid
fi

echo "停止Spark集群..."
$SPARK_HOME/sbin/stop-all.sh 2>/dev/null || true

echo "停止Flink应用..."
if [ -f /opt/apps/pid/flink_engine.pid ]; then
    kill $(cat /opt/apps/pid/flink_engine.pid) 2>/dev/null || true
    rm -f /opt/apps/pid/flink_engine.pid
fi

echo "停止Flink集群..."
$FLINK_HOME/bin/stop-cluster.sh 2>/dev/null || true

echo "所有应用已停止"
EOF

    chmod +x /opt/apps/stop_all_apps.sh
    
    log_success "停止脚本创建完成"
}

# 创建监控脚本
create_monitoring_script() {
    log_info "创建监控脚本..."
    
    cat > "/opt/apps/monitor_apps.sh" << 'EOF'
#!/bin/bash

# 应用监控脚本

echo "=========================================="
echo "Spark & Flink Standalone 应用监控"
echo "=========================================="

echo ""
echo "=== Spark集群状态 ==="
if pgrep -f "org.apache.spark.deploy.master.Master" >/dev/null; then
    echo "✓ Spark Master: 运行中"
else
    echo "✗ Spark Master: 未运行"
fi

if pgrep -f "org.apache.spark.deploy.worker.Worker" >/dev/null; then
    echo "✓ Spark Worker: 运行中"
else
    echo "✗ Spark Worker: 未运行"
fi

echo ""
echo "=== Spark应用状态 ==="
check_spark_app() {
    local app_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ] && kill -0 $(cat "$pid_file") 2>/dev/null; then
        echo "✓ $app_name: 运行中 (PID: $(cat $pid_file))"
    else
        echo "✗ $app_name: 未运行"
    fi
}

check_spark_app "ES持久化" "/opt/apps/pid/es_persistent.pid"
check_spark_app "CK流量持久化" "/opt/apps/pid/ck_traffic_persistent.pid"
check_spark_app "CK告警持久化" "/opt/apps/pid/ck_alarm_persistent.pid"
check_spark_app "解析器" "/opt/apps/pid/parser.pid"

echo ""
echo "=== Flink集群状态 ==="
if pgrep -f "org.apache.flink.runtime.entrypoint.StandaloneSessionClusterEntrypoint" >/dev/null; then
    echo "✓ Flink JobManager: 运行中"
else
    echo "✗ Flink JobManager: 未运行"
fi

if pgrep -f "org.apache.flink.runtime.taskexecutor.TaskManagerRunner" >/dev/null; then
    echo "✓ Flink TaskManager: 运行中"
else
    echo "✗ Flink TaskManager: 未运行"
fi

echo ""
echo "=== 内存使用情况 ==="
echo "Spark进程内存:"
ps aux | grep -E "(spark|Master|Worker)" | grep -v grep | awk '{sum+=$6} END {print "  总计: " sum/1024 " MB"}'

echo "Flink进程内存:"
ps aux | grep -E "flink" | grep -v grep | awk '{sum+=$6} END {print "  总计: " sum/1024 " MB"}'

echo ""
echo "=== Web UI地址 ==="
echo "Spark Master UI: http://localhost:8080"
echo "Flink Dashboard: http://localhost:8082"

echo ""
echo "=========================================="
EOF

    chmod +x /opt/apps/monitor_apps.sh
    
    log_success "监控脚本创建完成"
}

# 主函数
main() {
    echo "=========================================="
    echo "Spark & Flink Standalone模式迁移"
    echo "=========================================="
    
    create_directories
    analyze_current_spark_apps
    analyze_current_flink_apps
    create_spark_standalone_config
    create_flink_standalone_config
    create_application_scripts
    create_stop_scripts
    create_monitoring_script
    
    echo ""
    echo "=========================================="
    echo "迁移脚本创建完成!"
    echo "=========================================="
    echo ""
    echo "下一步操作:"
    echo "1. 手动停止Hadoop服务"
    echo "2. 安装Spark和Flink到 /opt/spark 和 /opt/flink"
    echo "3. 运行: /opt/apps/start_spark_apps.sh"
    echo "4. 运行: /opt/apps/start_flink_apps.sh"
    echo "5. 监控: /opt/apps/monitor_apps.sh"
    echo ""
    echo "配置文件位置:"
    echo "- Spark配置: $SPARK_HOME/conf/"
    echo "- Flink配置: $FLINK_HOME/conf/"
    echo "- 备份目录: $BACKUP_DIR"
    echo ""
    echo "预期内存节省: ~8GB (从12GB降到4GB)"
    echo "=========================================="
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
