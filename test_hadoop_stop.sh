#!/bin/bash

# Hadoop服务停止测试脚本
# 用于安全测试停止Hadoop服务的效果

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%H:%M:%S') $1"
}

# 记录当前状态
record_current_state() {
    log_info "记录当前系统状态..."
    
    echo "=== 当前时间 ===" > /tmp/hadoop_test_before.log
    date >> /tmp/hadoop_test_before.log
    
    echo "=== 内存使用 ===" >> /tmp/hadoop_test_before.log
    free -h >> /tmp/hadoop_test_before.log
    
    echo "=== Hadoop进程 ===" >> /tmp/hadoop_test_before.log
    ps aux | grep -E "(hadoop|yarn|hdfs)" | grep -v grep >> /tmp/hadoop_test_before.log || echo "无Hadoop进程" >> /tmp/hadoop_test_before.log
    
    echo "=== 端口占用 ===" >> /tmp/hadoop_test_before.log
    netstat -tlnp | grep -E "(8020|8030|8040|8088)" >> /tmp/hadoop_test_before.log || echo "无Hadoop端口" >> /tmp/hadoop_test_before.log
    
    echo "=== 核心服务状态 ===" >> /tmp/hadoop_test_before.log
    systemctl is-active clickhouse-server 2>/dev/null >> /tmp/hadoop_test_before.log || echo "clickhouse: unknown" >> /tmp/hadoop_test_before.log
    curl -s localhost:9200/_cluster/health | jq .status 2>/dev/null >> /tmp/hadoop_test_before.log || echo "elasticsearch: unknown" >> /tmp/hadoop_test_before.log
    
    log_success "状态记录完成: /tmp/hadoop_test_before.log"
}

# 显示当前Hadoop进程详情
show_hadoop_processes() {
    log_info "当前Hadoop相关进程详情:"
    
    echo "进程列表:"
    ps aux | grep -E "(hadoop|yarn|hdfs)" | grep -v grep | while read line; do
        pid=$(echo $line | awk '{print $2}')
        mem=$(echo $line | awk '{print $6}')
        mem_mb=$((mem / 1024))
        cmd=$(echo $line | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
        echo "  PID: $pid, 内存: ${mem_mb}MB, 命令: $(echo $cmd | cut -c1-80)..."
    done
    
    echo ""
    echo "内存统计:"
    total_mem=$(ps aux | grep -E "(hadoop|yarn|hdfs)" | grep -v grep | awk '{sum+=$6} END {print sum/1024}')
    echo "  Hadoop总内存使用: ${total_mem}MB"
}

# 测试停止单个Hadoop服务
test_stop_single_service() {
    local service_name=$1
    local pid=$2
    
    log_info "测试停止 $service_name (PID: $pid)..."
    
    # 记录停止前内存
    mem_before=$(free -m | awk 'NR==2{print $3}')
    
    # 发送TERM信号
    if kill -TERM "$pid" 2>/dev/null; then
        log_info "已发送停止信号给 $service_name"
        
        # 等待进程退出
        for i in {1..10}; do
            if ! kill -0 "$pid" 2>/dev/null; then
                log_success "$service_name 已停止"
                break
            fi
            sleep 1
        done
        
        # 检查是否仍在运行
        if kill -0 "$pid" 2>/dev/null; then
            log_warn "$service_name 仍在运行，可能需要强制停止"
            return 1
        fi
        
        # 记录停止后内存
        sleep 2
        mem_after=$(free -m | awk 'NR==2{print $3}')
        mem_saved=$((mem_before - mem_after))
        
        if [ $mem_saved -gt 0 ]; then
            log_success "停止 $service_name 节省内存: ${mem_saved}MB"
        fi
        
        return 0
    else
        log_warn "无法停止 $service_name (PID: $pid)"
        return 1
    fi
}

# 验证核心服务状态
verify_core_services() {
    log_info "验证核心服务状态..."
    
    # ClickHouse
    if pgrep clickhouse >/dev/null; then
        if clickhouse-client --query "SELECT 1" >/dev/null 2>&1; then
            log_success "ClickHouse: 运行正常"
        else
            log_warn "ClickHouse: 进程存在但查询失败"
        fi
    else
        log_warn "ClickHouse: 进程不存在"
    fi
    
    # Elasticsearch
    if pgrep -f elasticsearch >/dev/null; then
        if curl -s localhost:9200/_cluster/health >/dev/null 2>&1; then
            log_success "Elasticsearch: 运行正常"
        else
            log_warn "Elasticsearch: 进程存在但API不响应"
        fi
    else
        log_warn "Elasticsearch: 进程不存在"
    fi
    
    # Kafka
    if pgrep -f kafka >/dev/null; then
        log_success "Kafka: 进程运行中"
    else
        log_warn "Kafka: 进程不存在"
    fi
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    cat > /tmp/hadoop_stop_test_report.txt << EOF
Hadoop停止测试报告
==================

测试时间: $(date)

测试前状态:
$(cat /tmp/hadoop_test_before.log)

测试后状态:
-----------
内存使用: $(free -h | grep Mem)

剩余Hadoop进程:
$(ps aux | grep -E "(hadoop|yarn|hdfs)" | grep -v grep || echo "无剩余Hadoop进程")

端口占用:
$(netstat -tlnp | grep -E "(8020|8030|8040|8088)" || echo "无Hadoop端口占用")

核心服务状态:
ClickHouse: $(pgrep clickhouse >/dev/null && echo "运行中" || echo "未运行")
Elasticsearch: $(pgrep -f elasticsearch >/dev/null && echo "运行中" || echo "未运行")  
Kafka: $(pgrep -f kafka >/dev/null && echo "运行中" || echo "未运行")

内存对比:
测试前: $(cat /tmp/hadoop_test_before.log | grep "Mem:" | awk '{print $3}')
测试后: $(free -h | grep Mem | awk '{print $3}')

结论:
$([ $(ps aux | grep -E "(hadoop|yarn|hdfs)" | grep -v grep | wc -l) -eq 0 ] && echo "✓ 所有Hadoop进程已停止" || echo "⚠ 仍有Hadoop进程运行")
$([ $(pgrep clickhouse | wc -l) -gt 0 ] && echo "✓ ClickHouse正常运行" || echo "✗ ClickHouse未运行")
$([ $(pgrep -f elasticsearch | wc -l) -gt 0 ] && echo "✓ Elasticsearch正常运行" || echo "✗ Elasticsearch未运行")
$([ $(pgrep -f kafka | wc -l) -gt 0 ] && echo "✓ Kafka正常运行" || echo "✗ Kafka未运行")

EOF

    log_success "测试报告已生成: /tmp/hadoop_stop_test_report.txt"
}

# 主测试函数
main() {
    echo "=========================================="
    echo "Hadoop服务停止测试"
    echo "=========================================="
    
    # 记录初始状态
    record_current_state
    
    # 显示当前Hadoop进程
    show_hadoop_processes
    
    echo ""
    read -p "是否继续停止Hadoop服务? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "测试取消"
        exit 0
    fi
    
    # 获取Hadoop进程列表
    hadoop_pids=$(ps aux | grep -E "org.apache.hadoop" | grep -v grep | awk '{print $2}' || true)
    
    if [ -z "$hadoop_pids" ]; then
        log_warn "未发现Hadoop进程"
        exit 0
    fi
    
    log_info "开始停止Hadoop服务..."
    
    # 逐个停止Hadoop进程
    for pid in $hadoop_pids; do
        service_name=$(ps -p $pid -o cmd= | grep -o "org.apache.hadoop.[^[:space:]]*" | sed 's/org.apache.hadoop.//')
        test_stop_single_service "$service_name" "$pid"
        sleep 2
    done
    
    # 等待系统稳定
    log_info "等待系统稳定..."
    sleep 10
    
    # 验证核心服务
    verify_core_services
    
    # 生成报告
    generate_test_report
    
    echo ""
    echo "=========================================="
    echo "测试完成!"
    echo "查看详细报告: cat /tmp/hadoop_stop_test_report.txt"
    echo "=========================================="
    
    # 显示简要结果
    echo ""
    echo "简要结果:"
    echo "内存使用变化: $(cat /tmp/hadoop_test_before.log | grep "Mem:" | awk '{print $3}') → $(free -h | grep Mem | awk '{print $3}')"
    echo "剩余Hadoop进程: $(ps aux | grep -E "(hadoop|yarn|hdfs)" | grep -v grep | wc -l) 个"
    echo "核心服务状态: ClickHouse($(pgrep clickhouse >/dev/null && echo "✓" || echo "✗")) Elasticsearch($(pgrep -f elasticsearch >/dev/null && echo "✓" || echo "✗")) Kafka($(pgrep -f kafka >/dev/null && echo "✓" || echo "✗"))"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
