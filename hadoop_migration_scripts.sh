#!/bin/bash

# Hadoop生态迁移自动化脚本
# 作者: ServerPerfAI
# 日期: $(date +%Y-%m-%d)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 全局变量
BACKUP_DIR="/backup/hadoop_migration_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/var/log/hadoop_migration.log"

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$BACKUP_DIR/configs"
    mkdir -p "$BACKUP_DIR/data"
    mkdir -p "$BACKUP_DIR/logs"
}

# 系统状态检查
check_system_status() {
    log_info "检查系统当前状态..."
    
    # 检查内存使用
    log_info "当前内存使用情况:"
    free -h
    
    # 检查Hadoop进程
    log_info "当前Hadoop相关进程:"
    ps aux | grep -E "(hadoop|yarn|hdfs)" | grep -v grep || log_warn "未发现Hadoop进程"
    
    # 检查端口占用
    log_info "检查Hadoop相关端口:"
    netstat -tlnp | grep -E "(8020|8030|8040|8088|9000)" || log_warn "未发现Hadoop端口占用"
    
    # 检查磁盘使用
    log_info "磁盘使用情况:"
    df -h
}

# 备份配置文件
backup_configurations() {
    log_info "备份配置文件..."
    
    # 备份Spark配置
    find /home -name "*spark*" -type f \( -name "*.conf" -o -name "*.properties" -o -name "*.yaml" \) 2>/dev/null | \
        xargs -I {} cp {} "$BACKUP_DIR/configs/" 2>/dev/null || log_warn "Spark配置备份可能不完整"
    
    # 备份Flink配置
    find /home -name "*flink*" -type f \( -name "*.conf" -o -name "*.properties" -o -name "*.yaml" \) 2>/dev/null | \
        xargs -I {} cp {} "$BACKUP_DIR/configs/" 2>/dev/null || log_warn "Flink配置备份可能不完整"
    
    # 备份系统配置
    cp /etc/security/limits.conf "$BACKUP_DIR/configs/" 2>/dev/null || log_warn "limits.conf备份失败"
    cp /etc/sysctl.conf "$BACKUP_DIR/configs/" 2>/dev/null || log_warn "sysctl.conf备份失败"
    
    log_success "配置文件备份完成"
}

# 停止Hadoop服务
stop_hadoop_services() {
    log_info "开始停止Hadoop服务..."
    
    # 获取Hadoop相关进程PID
    HADOOP_PIDS=$(ps aux | grep -E "org.apache.hadoop" | grep -v grep | awk '{print $2}' || true)
    
    if [ -n "$HADOOP_PIDS" ]; then
        log_info "发现Hadoop进程: $HADOOP_PIDS"
        
        # 优雅停止
        for pid in $HADOOP_PIDS; do
            log_info "停止进程 PID: $pid"
            kill -TERM "$pid" 2>/dev/null || log_warn "无法停止进程 $pid"
        done
        
        # 等待进程退出
        sleep 10
        
        # 强制停止仍在运行的进程
        REMAINING_PIDS=$(ps aux | grep -E "org.apache.hadoop" | grep -v grep | awk '{print $2}' || true)
        if [ -n "$REMAINING_PIDS" ]; then
            log_warn "强制停止剩余进程: $REMAINING_PIDS"
            for pid in $REMAINING_PIDS; do
                kill -KILL "$pid" 2>/dev/null || log_warn "无法强制停止进程 $pid"
            done
        fi
        
        log_success "Hadoop服务已停止"
    else
        log_info "未发现运行中的Hadoop服务"
    fi
}

# 停止Spark on YARN应用
stop_spark_applications() {
    log_info "停止Spark应用..."
    
    # 获取Spark相关进程
    SPARK_PIDS=$(ps aux | grep -E "spark.*yarn" | grep -v grep | awk '{print $2}' || true)
    
    if [ -n "$SPARK_PIDS" ]; then
        log_info "发现Spark on YARN进程: $SPARK_PIDS"
        for pid in $SPARK_PIDS; do
            log_info "停止Spark进程 PID: $pid"
            kill -TERM "$pid" 2>/dev/null || log_warn "无法停止Spark进程 $pid"
        done
        sleep 5
        log_success "Spark应用已停止"
    else
        log_info "未发现Spark on YARN应用"
    fi
}

# 停止Flink集群应用
stop_flink_applications() {
    log_info "停止Flink应用..."
    
    # 获取Flink相关进程
    FLINK_PIDS=$(ps aux | grep -E "flink.*yarn" | grep -v grep | awk '{print $2}' || true)
    
    if [ -n "$FLINK_PIDS" ]; then
        log_info "发现Flink集群进程: $FLINK_PIDS"
        for pid in $FLINK_PIDS; do
            log_info "停止Flink进程 PID: $pid"
            kill -TERM "$pid" 2>/dev/null || log_warn "无法停止Flink进程 $pid"
        done
        sleep 5
        log_success "Flink应用已停止"
    else
        log_info "未发现Flink集群应用"
    fi
}

# 验证核心服务状态
verify_core_services() {
    log_info "验证核心服务状态..."
    
    # 检查ClickHouse
    if command -v clickhouse-client >/dev/null 2>&1; then
        if clickhouse-client --query "SELECT 1" >/dev/null 2>&1; then
            log_success "ClickHouse服务正常"
        else
            log_error "ClickHouse服务异常"
            return 1
        fi
    else
        log_warn "未找到ClickHouse客户端"
    fi
    
    # 检查Elasticsearch
    if curl -s -X GET "localhost:9200/_cluster/health" >/dev/null 2>&1; then
        log_success "Elasticsearch服务正常"
    else
        log_warn "Elasticsearch服务可能异常"
    fi
    
    # 检查Kafka
    if command -v kafka-topics.sh >/dev/null 2>&1; then
        if kafka-topics.sh --list --bootstrap-server localhost:9092 >/dev/null 2>&1; then
            log_success "Kafka服务正常"
        else
            log_warn "Kafka服务可能异常"
        fi
    else
        log_warn "未找到Kafka工具"
    fi
}

# 监控内存变化
monitor_memory_usage() {
    log_info "监控内存使用变化..."
    
    # 记录停止前内存使用
    MEMORY_BEFORE=$(free -m | awk 'NR==2{printf "%.1f", $3/1024}')
    log_info "停止前内存使用: ${MEMORY_BEFORE}GB"
    
    # 等待系统稳定
    sleep 30
    
    # 记录停止后内存使用
    MEMORY_AFTER=$(free -m | awk 'NR==2{printf "%.1f", $3/1024}')
    log_info "停止后内存使用: ${MEMORY_AFTER}GB"
    
    # 计算节省的内存
    MEMORY_SAVED=$(echo "$MEMORY_BEFORE - $MEMORY_AFTER" | bc)
    log_success "节省内存: ${MEMORY_SAVED}GB"
    
    # 显示详细内存信息
    log_info "当前内存详情:"
    free -h
}

# 清理Hadoop残留
cleanup_hadoop_remnants() {
    log_info "清理Hadoop残留文件..."
    
    # 清理临时文件
    rm -rf /tmp/hadoop-* 2>/dev/null || log_warn "清理/tmp/hadoop-*失败"
    rm -rf /tmp/hsperfdata_* 2>/dev/null || log_warn "清理/tmp/hsperfdata_*失败"
    
    # 清理日志文件(可选)
    # find /var/log -name "*hadoop*" -type f -mtime +7 -delete 2>/dev/null || log_warn "清理旧日志失败"
    
    log_success "Hadoop残留清理完成"
}

# 生成迁移报告
generate_migration_report() {
    local report_file="$BACKUP_DIR/migration_report.txt"
    log_info "生成迁移报告: $report_file"
    
    cat > "$report_file" << EOF
Hadoop生态迁移报告
==================

迁移时间: $(date)
备份目录: $BACKUP_DIR

迁移前状态:
-----------
$(cat "$BACKUP_DIR/logs/before_migration.log" 2>/dev/null || echo "无迁移前状态记录")

迁移后状态:
-----------
内存使用: $(free -h | grep Mem)
运行进程: $(ps aux | grep -E "(java|python)" | grep -v grep | wc -l) 个Java/Python进程

已停止的服务:
------------
- Hadoop NameNode
- Hadoop DataNode  
- Hadoop ResourceManager
- Hadoop NodeManager
- Hadoop SecondaryNameNode
- Spark on YARN 应用
- Flink 集群应用

仍在运行的核心服务:
------------------
- ClickHouse: $(pgrep clickhouse >/dev/null && echo "运行中" || echo "未运行")
- Elasticsearch: $(pgrep java | xargs -I {} ps -p {} -o cmd= | grep elasticsearch >/dev/null && echo "运行中" || echo "未运行")
- Kafka: $(pgrep java | xargs -I {} ps -p {} -o cmd= | grep kafka >/dev/null && echo "运行中" || echo "未运行")

下一步建议:
-----------
1. 配置Spark Standalone模式
2. 配置Flink单机模式  
3. 更新应用配置文件
4. 重启相关应用服务
5. 验证数据流正常

EOF

    log_success "迁移报告已生成"
}

# 主函数
main() {
    log_info "开始Hadoop生态迁移..."
    
    # 记录开始时间
    START_TIME=$(date +%s)
    
    # 保存迁移前状态
    echo "=== 迁移前系统状态 ===" > "$BACKUP_DIR/logs/before_migration.log"
    free -h >> "$BACKUP_DIR/logs/before_migration.log"
    ps aux | grep -E "(hadoop|spark|flink)" | grep -v grep >> "$BACKUP_DIR/logs/before_migration.log"
    
    # 执行迁移步骤
    create_backup_dir
    check_system_status
    backup_configurations
    stop_spark_applications
    stop_flink_applications  
    stop_hadoop_services
    verify_core_services
    monitor_memory_usage
    cleanup_hadoop_remnants
    generate_migration_report
    
    # 计算总耗时
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    log_success "Hadoop生态迁移完成!"
    log_info "总耗时: ${DURATION}秒"
    log_info "备份目录: $BACKUP_DIR"
    log_info "迁移报告: $BACKUP_DIR/migration_report.txt"
    
    echo ""
    echo "=========================================="
    echo "迁移完成! 请检查以下内容:"
    echo "1. 核心服务是否正常运行"
    echo "2. 内存使用是否显著下降"  
    echo "3. 查看迁移报告了解详细信息"
    echo "=========================================="
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # 检查是否以root权限运行
    if [[ $EUID -ne 0 ]]; then
        log_error "请以root权限运行此脚本"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 执行主函数并记录日志
    main 2>&1 | tee "$LOG_FILE"
fi
