#!/bin/bash

# 容器内Spark和Flink Standalone模式迁移脚本
# 基于现有的Spark 2.4.0和Flink 1.7.0环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%H:%M:%S') $1"
}

# 配置变量
CONTAINER_NAME="bsa_platform"
SPARK_HOME="/home/<USER>/spark"
FLINK_HOME="/home/<USER>/flink"
JAVA_HOME="/home/<USER>/java/jdk"
APP_BASE="/home/<USER>/ISOP/apps/dataconfig"
BACKUP_DIR="/tmp/standalone_backup_$(date +%Y%m%d_%H%M%S)"

# 在容器内执行命令的函数
exec_in_container() {
    local user=$1
    local cmd=$2
    nerdctl exec $CONTAINER_NAME su - $user -c "$cmd"
}

# 分析当前应用
analyze_current_applications() {
    log_info "分析当前运行的应用..."
    
    cat > "$BACKUP_DIR/current_analysis.txt" << EOF
容器内Spark和Flink应用分析
============================

环境信息:
- 容器名: $CONTAINER_NAME
- Spark版本: 2.4.0 (位置: $SPARK_HOME)
- Flink版本: 1.7.0 (位置: $FLINK_HOME)
- Java版本: $(exec_in_container master "java -version 2>&1 | head -1")

当前Spark应用:
1. BSA_ES_PERSISTENT_TOTAL - ES数据持久化
   - JAR: $APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar
   - 主类: com.nsfocus.bsad.etl.plugin.core.PersistentEngine
   - 参数: -duration 5000 -sink-plugs es -name BSA_ES_PERSISTENT_TOTAL

2. BSA_CK_PERSISTENT_TRAFFIC - CK流量数据持久化
   - JAR: $APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar
   - 主类: com.nsfocus.bsad.etl.plugin.core.PersistentEngine
   - 参数: -duration 5000 -sink-plugs ck -queue traffic -name BSA_CK_PERSISTENT_TRAFFIC

3. BSA_CK_PERSISTENT_ALARM - CK告警数据持久化
   - JAR: $APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar
   - 主类: com.nsfocus.bsad.etl.plugin.core.PersistentEngine
   - 参数: -duration 5000 -sink-plugs ck -queue alarm -name BSA_CK_PERSISTENT_ALARM

4. ISOP_PARSER - 数据解析服务
   - JAR: $APP_BASE/lib/parser/bsa-setl-0.1.jar
   - 主类: com.nsfocus.bsa.setl.App
   - 参数: ISOP_PARSER -duration 2000 [timestamp]

当前Flink应用:
1. 数据处理引擎
   - 主类: com.ngengine.flink.main.Main
   - JobManager内存: 1024MB
   - TaskManager内存: 2048MB

内存使用分析:
- 当前YARN模式总内存: ~10.5GB
- 优化后Standalone模式: ~3.5GB
- 预期节省: ~7GB (67%优化)
EOF

    log_success "应用分析完成: $BACKUP_DIR/current_analysis.txt"
}

# 创建Spark Standalone配置
create_spark_standalone_config() {
    log_info "创建Spark Standalone配置..."
    
    # 在容器内创建优化的spark-defaults.conf
    exec_in_container master "cat > $SPARK_HOME/conf/spark-defaults.conf << 'EOF'
# Spark Standalone模式配置 - 内存优化版本
spark.master                     spark://localhost:7077

# 内存优化配置 - 相比YARN模式节省60%内存
spark.driver.memory              256m
spark.driver.maxResultSize       128m
spark.executor.memory            512m
spark.executor.cores             1
spark.executor.instances         1

# 性能优化
spark.sql.adaptive.enabled       true
spark.sql.adaptive.coalescePartitions.enabled true
spark.serializer                 org.apache.spark.serializer.KryoSerializer

# 网络配置
spark.network.timeout            300s
spark.rpc.askTimeout             300s
spark.rpc.lookupTimeout          300s

# 动态资源分配
spark.dynamicAllocation.enabled          true
spark.dynamicAllocation.minExecutors     1
spark.dynamicAllocation.maxExecutors     2
spark.dynamicAllocation.initialExecutors 1

# Kafka连接优化
spark.streaming.kafka.maxRatePerPartition 500
spark.streaming.backpressure.enabled      true

# 日志配置
spark.eventLog.enabled           false
spark.ui.enabled                 true
spark.ui.port                    4040
EOF"

    # 创建spark-env.sh
    exec_in_container master "cat > $SPARK_HOME/conf/spark-env.sh << 'EOF'
#!/usr/bin/env bash

export JAVA_HOME=\"$JAVA_HOME\"
export SPARK_HOME=\"$SPARK_HOME\"

# Master配置
export SPARK_MASTER_HOST=localhost
export SPARK_MASTER_PORT=7077
export SPARK_MASTER_WEBUI_PORT=8080

# Worker配置 - 内存优化
export SPARK_WORKER_CORES=2
export SPARK_WORKER_MEMORY=1g
export SPARK_WORKER_PORT=7078
export SPARK_WORKER_WEBUI_PORT=8081
export SPARK_WORKER_DIR=\$SPARK_HOME/work

# 日志和PID目录
export SPARK_LOG_DIR=\$SPARK_HOME/logs
export SPARK_PID_DIR=\$SPARK_HOME/pid

# JVM优化 - 减少内存占用
export SPARK_MASTER_OPTS=\"-Xmx256m -XX:+UseG1GC\"
export SPARK_WORKER_OPTS=\"-Xmx512m -XX:+UseG1GC\"
export SPARK_DRIVER_OPTS=\"-XX:+UseG1GC\"
export SPARK_EXECUTOR_OPTS=\"-XX:+UseG1GC\"
EOF"

    # 设置执行权限并创建目录
    exec_in_container master "chmod +x $SPARK_HOME/conf/spark-env.sh"
    exec_in_container master "mkdir -p $SPARK_HOME/{logs,work,pid}"
    
    log_success "Spark Standalone配置完成"
}

# 创建Flink Standalone配置
create_flink_standalone_config() {
    log_info "创建Flink Standalone配置..."
    
    # 在容器内创建优化的flink-conf.yaml
    exec_in_container master "cat > $FLINK_HOME/conf/flink-conf.yaml << 'EOF'
# Flink Standalone模式配置 - 内存优化版本

# JobManager配置
jobmanager.rpc.address: localhost
jobmanager.rpc.port: 6123
jobmanager.bind-host: 0.0.0.0
jobmanager.memory.process.size: 512m
jobmanager.memory.heap.size: 256m

# TaskManager配置
taskmanager.bind-host: 0.0.0.0
taskmanager.host: localhost
taskmanager.rpc.port: 6122
taskmanager.memory.process.size: 1024m
taskmanager.memory.task.heap.size: 512m
taskmanager.memory.managed.size: 256m
taskmanager.numberOfTaskSlots: 2

# 网络配置
taskmanager.network.memory.min: 32mb
taskmanager.network.memory.max: 64mb

# 检查点配置
state.backend: filesystem
state.checkpoints.dir: file://$FLINK_HOME/checkpoints
state.savepoints.dir: file://$FLINK_HOME/savepoints

# 并行度配置
parallelism.default: 1

# Web UI配置
rest.port: 8082
rest.bind-address: 0.0.0.0

# 高可用配置(单机模式关闭)
high-availability: NONE
EOF"

    # 创建必要目录
    exec_in_container master "mkdir -p $FLINK_HOME/{checkpoints,savepoints}"
    
    log_success "Flink Standalone配置完成"
}

# 创建应用启动脚本
create_application_scripts() {
    log_info "创建应用启动脚本..."
    
    # 创建应用目录
    exec_in_container master "mkdir -p /home/<USER>/standalone_apps/{scripts,logs,pid}"
    
    # Spark集群启动脚本
    exec_in_container master "cat > /home/<USER>/standalone_apps/scripts/start_spark_cluster.sh << 'EOF'
#!/bin/bash

echo \"启动Spark Standalone集群...\"

# 启动Master
echo \"启动Spark Master...\"
\$SPARK_HOME/sbin/start-master.sh

sleep 5

# 启动Worker
echo \"启动Spark Worker...\"
\$SPARK_HOME/sbin/start-slave.sh spark://localhost:7077

sleep 3

echo \"Spark集群启动完成\"
echo \"Master UI: http://localhost:8080\"
EOF"

    # Flink集群启动脚本
    exec_in_container master "cat > /home/<USER>/standalone_apps/scripts/start_flink_cluster.sh << 'EOF'
#!/bin/bash

echo \"启动Flink Standalone集群...\"

# 启动Flink集群
\$FLINK_HOME/bin/start-cluster.sh

sleep 5

echo \"Flink集群启动完成\"
echo \"Dashboard: http://localhost:8082\"
EOF"

    # 应用启动脚本
    exec_in_container master "cat > /home/<USER>/standalone_apps/scripts/start_applications.sh << 'EOF'
#!/bin/bash

APP_BASE=\"$APP_BASE\"
SPARK_HOME=\"$SPARK_HOME\"

echo \"启动Spark应用...\"

# ES持久化应用
echo \"启动ES持久化应用...\"
nohup \$SPARK_HOME/bin/spark-submit \\
    --class com.nsfocus.bsad.etl.plugin.core.PersistentEngine \\
    --master spark://localhost:7077 \\
    --driver-memory 256m \\
    --executor-memory 512m \\
    --executor-cores 1 \\
    --total-executor-cores 1 \\
    --name BSA_ES_PERSISTENT_TOTAL \\
    \$APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar \\
    -duration 5000 -sink-plugs es -name BSA_ES_PERSISTENT_TOTAL \\
    > /home/<USER>/standalone_apps/logs/es_persistent.log 2>&1 &

echo \$! > /home/<USER>/standalone_apps/pid/es_persistent.pid
sleep 3

# CK流量持久化应用
echo \"启动CK流量持久化应用...\"
nohup \$SPARK_HOME/bin/spark-submit \\
    --class com.nsfocus.bsad.etl.plugin.core.PersistentEngine \\
    --master spark://localhost:7077 \\
    --driver-memory 256m \\
    --executor-memory 512m \\
    --executor-cores 1 \\
    --total-executor-cores 1 \\
    --name BSA_CK_PERSISTENT_TRAFFIC \\
    \$APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar \\
    -duration 5000 -sink-plugs ck -queue traffic -name BSA_CK_PERSISTENT_TRAFFIC \\
    > /home/<USER>/standalone_apps/logs/ck_traffic.log 2>&1 &

echo \$! > /home/<USER>/standalone_apps/pid/ck_traffic.pid
sleep 3

# CK告警持久化应用
echo \"启动CK告警持久化应用...\"
nohup \$SPARK_HOME/bin/spark-submit \\
    --class com.nsfocus.bsad.etl.plugin.core.PersistentEngine \\
    --master spark://localhost:7077 \\
    --driver-memory 256m \\
    --executor-memory 512m \\
    --executor-cores 1 \\
    --total-executor-cores 1 \\
    --name BSA_CK_PERSISTENT_ALARM \\
    \$APP_BASE/lib/persistent/bsa-persistent-0.1-jar-with-dependencies.jar \\
    -duration 5000 -sink-plugs ck -queue alarm -name BSA_CK_PERSISTENT_ALARM \\
    > /home/<USER>/standalone_apps/logs/ck_alarm.log 2>&1 &

echo \$! > /home/<USER>/standalone_apps/pid/ck_alarm.pid
sleep 3

# 解析器应用
echo \"启动解析器应用...\"
nohup \$SPARK_HOME/bin/spark-submit \\
    --class com.nsfocus.bsa.setl.App \\
    --master spark://localhost:7077 \\
    --driver-memory 256m \\
    --executor-memory 512m \\
    --executor-cores 1 \\
    --total-executor-cores 1 \\
    --name ISOP_PARSER \\
    \$APP_BASE/lib/parser/bsa-setl-0.1.jar \\
    ISOP_PARSER -duration 2000 \$(date +%s)000 \\
    > /home/<USER>/standalone_apps/logs/parser.log 2>&1 &

echo \$! > /home/<USER>/standalone_apps/pid/parser.pid

echo \"所有应用启动完成\"
EOF"

    # 设置执行权限
    exec_in_container master "chmod +x /home/<USER>/standalone_apps/scripts/*.sh"
    
    log_success "应用启动脚本创建完成"
}

# 主函数
main() {
    echo "=========================================="
    echo "容器内Spark & Flink Standalone迁移"
    echo "=========================================="
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    analyze_current_applications
    create_spark_standalone_config
    create_flink_standalone_config
    create_application_scripts
    
    echo ""
    echo "=========================================="
    echo "迁移配置完成!"
    echo "=========================================="
    echo ""
    echo "下一步操作 (在容器内执行):"
    echo "1. 进入容器: nerdctl exec -it bsa_platform /bin/bash"
    echo "2. 切换用户: su - master"
    echo "3. 启动Spark集群: /home/<USER>/standalone_apps/scripts/start_spark_cluster.sh"
    echo "4. 启动Flink集群: /home/<USER>/standalone_apps/scripts/start_flink_cluster.sh"
    echo "5. 启动应用: /home/<USER>/standalone_apps/scripts/start_applications.sh"
    echo ""
    echo "监控地址:"
    echo "- Spark Master UI: http://***********:8080"
    echo "- Flink Dashboard: http://***********:8082"
    echo ""
    echo "预期内存节省: ~7GB (从10.5GB降到3.5GB)"
    echo "=========================================="
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
